<?php
// downloadReport.php - serves saved reports for download or viewing
// Expected GET param: file (filename without path)
// Optional GET param: action (download or view, defaults to download)

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$filename = isset($_GET['file']) ? trim($_GET['file']) : '';
$action = isset($_GET['action']) ? trim($_GET['action']) : 'download';

// Validate filename (only allow PDF files with specific pattern)
if (empty($filename) || !preg_match('/^blesk_report_[a-z]+_\d{8}_\d{6}\.pdf$/', $filename)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid filename']);
    exit;
}

$REPORTS_DIR = __DIR__ . '/saved_reports';
$filepath = $REPORTS_DIR . '/' . $filename;

// Check if file exists
if (!file_exists($filepath) || !is_file($filepath)) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'File not found']);
    exit;
}

// Get file size
$fileSize = filesize($filepath);
if ($fileSize === false) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Unable to read file']);
    exit;
}

// Check if metadata file exists
$metadataFile = $REPORTS_DIR . '/' . pathinfo($filename, PATHINFO_FILENAME) . '.json';
$metadata = null;
if (file_exists($metadataFile)) {
    $content = file_get_contents($metadataFile);
    if ($content !== false) {
        $metadata = json_decode($content, true);
    }
}

// Set appropriate headers based on action
if ($action === 'view') {
    header('Content-Type: application/pdf');
    header('Content-Disposition: inline; filename="' . $filename . '"');
} else {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
}

header('Content-Length: ' . $fileSize);
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Expires: 0');

// Clean output buffer
ob_clean();
flush();

// Read the file and output it to the browser
readfile($filepath);
exit;
?> 