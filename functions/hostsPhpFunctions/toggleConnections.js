/*
 * Toggle Connections Visibility on hosts.php page
 * Adds a new button to the header that shows / hides parent-child connection arrows.
 * The visibility state is stored on window.showConnections (default: true).
 */

(function () {
    // Ensure we only run once DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
        const headerButtons = document.querySelector('.header-buttons');
        if (!headerButtons) return; // Safety check

        // Create the toggle button
        const toggleBtn = document.createElement('a');
        toggleBtn.href = '#';
        toggleBtn.className = 'header-button';
        toggleBtn.id = 'toggle-connections-btn';
        toggleBtn.innerHTML = '<i class="fa fa-link"></i> Connections';

        // Insert the button just before the settings button (or append as fallback)
        const settingsBtn = document.getElementById('openSettingsBtn');
        if (settingsBtn && settingsBtn.parentNode === headerButtons) {
            headerButtons.insertBefore(toggleBtn, settingsBtn);
        } else {
            headerButtons.appendChild(toggleBtn);
        }

        // State variable (default: arrows are visible)
        if (typeof window.showConnections === 'undefined') {
            window.showConnections = true;
        }

        // Helper to update arrow-group visibility & button appearance
        function updateConnectionsVisibility() {
            const arrowGroup = document.querySelector('g.arrow-group');
            if (arrowGroup) {
                arrowGroup.style.display = window.showConnections ? '' : 'none';
            }
            // Update button appearance
            if (window.showConnections) {
                toggleBtn.classList.remove('connection-off');
            } else {
                toggleBtn.classList.add('connection-off');
            }
        }

        // Toggle logic
        toggleBtn.addEventListener('click', function (e) {
            e.preventDefault();
            window.showConnections = !window.showConnections;
            updateConnectionsVisibility();
        });

        // Initial attempt (arrowGroup may not exist yet)
        updateConnectionsVisibility();

        // Observe the canvas container for arrowGroup creation / re-render
        const canvasContainer = document.getElementById('canvas-container');
        if (canvasContainer) {
            const observer = new MutationObserver(updateConnectionsVisibility);
            observer.observe(canvasContainer, { childList: true, subtree: true });
        }
    });
})(); 