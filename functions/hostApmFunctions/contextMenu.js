async function generateContextMenu(serviceEncodedName, hostname, isHost = false, statusData = null) {
    const baseUrl = `https://${window.location.hostname}/nagios/cgi-bin/cmd.cgi`;
    
    let itemData = statusData;

    // If data is not passed directly, fetch it (fallback for other pages)
    if (!itemData) {
        const url = isHost ?
            `get_status_dat_info.php?type=host&hostname=${hostname}` :
            `get_status_dat_info.php?type=service&hostname=${hostname}&serviceDescription=${decodeURIComponent(serviceEncodedName)}`;
        
        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error('Failed to fetch details from status.dat');
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            itemData = data;
        } catch (error) {
            console.error('Error fetching data for context menu:', error);
            return `<div style="background: #fff; border-radius: 8px; padding: 12px; width: 260px; font-family: Arial, sans-serif; color: #ff4444;">Error loading commands: ${error.message}</div>`;
        }
    }

    try {
        if (isHost) {
            const host = itemData;
            const hostCommandList = [{
                icon: "fa-toggle-off",
                cmd: 47,
                text: "Enable active checks of this host",
                condition: host.active_checks_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 48,
                text: "Disable active checks of this host",
                condition: host.active_checks_enabled === '1'
            },
            {
                icon: "fa-clock-o",
                cmd: 96,
                text: "Re-schedule the next check of this host"
            },
            {
                icon: "fa-play-circle",
                cmd: 92,
                text: "Start accepting passive checks for this host",
                condition: host.passive_checks_enabled === '0'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 93,
                text: "Stop accepting passive checks for this host",
                condition: host.passive_checks_enabled === '1'
            },
            {
                icon: "fa-play-circle",
                cmd: 101,
                text: "Start obsessing over this host",
                condition: host.obsess === '0'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 102,
                text: "Stop obsessing over this host",
                condition: host.obsess === '1'
            },
            {
                icon: "fa-gavel",
                cmd: 33,
                text: "Acknowledge this host problem",
                condition: host.current_state != '0' && host.problem_has_been_acknowledged === '0'
            },
            {
                icon: "fa-gavel",
                cmd: 51,
                text: "Remove problem acknowledgement",
                condition: host.problem_has_been_acknowledged === '1'
            },
            {
                icon: "fa-microphone-slash",
                cmd: 24,
                text: "Enable notifications for this host",
                condition: host.notifications_enabled === '0'
            },
            {
                icon: "fa-microphone",
                cmd: 25,
                text: "Disable notifications for this host",
                condition: host.notifications_enabled === '1'
            },
            {
                icon: "fa-bullhorn",
                cmd: 159,
                text: "Send custom host notification"
            },
            {
                icon: "fa-moon-o",
                cmd: 55,
                text: "Schedule downtime for this host"
            },
            {
                icon: "fa-moon-o",
                cmd: 86,
                text: "Schedule downtime for all services on this host"
            },
            {
                icon: "fa-microphone-slash",
                cmd: 29,
                text: "Disable notifications for all services on this host"
            },
            {
                icon: "fa-microphone",
                cmd: 28,
                text: "Enable notifications for all services on this host"
            },
            {
                icon: "fa-clock-o",
                cmd: 17,
                text: "Schedule a check of all services on this host"
            },
            {
                icon: "fa-dot-circle-o",
                cmd: 16,
                text: "Disable checks of all services on this host"
            },
            {
                icon: "fa-circle-o",
                cmd: 15,
                text: "Enable checks of all services on this host"
            },
            {
                icon: "fa-toggle-off",
                cmd: 43,
                text: "Enable event handler for this host",
                condition: host.event_handler_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 44,
                text: "Disable event handler for this host",
                condition: host.event_handler_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 57,
                text: "Enable flap detection for this host",
                condition: host.flap_detection_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 58,
                text: "Disable flap detection for this host",
                condition: host.flap_detection_enabled === '1'
            },
            {
                icon: "fa-eraser",
                cmd: 173,
                text: "Clear flapping state for this host",
                condition: host.flap_detection_enabled === '1'
            },
            {
                icon: "fa-trash",
                cmd: 10000, // Custom command for blacklist
                text: "Delete Host"
            }
            ];

            return `
                    <div class="commands-card">
                        <h5 class="card-title">Host Commands</h5>
                        <ul class="command-list">
                            ${hostCommandList
                    .filter(cmd => cmd.condition !== undefined ? cmd.condition : true)
                    .map(cmd => {
                        if (cmd.cmd === 10000) {
                            // Special handling for blacklist command
                            return `
                                        <a href="#" class="command-link" onclick="blacklistHostApm('${hostname}'); return false;">
                                            <li class="command-item">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else {
                            return `
                                        <a href="#" class="command-link" onclick="openModalWithIframe('${baseUrl}?cmd_typ=${cmd.cmd}&host=${hostname}', '${cmd.text}'); return false;">
                                            <li class="command-item">
                                                <i class="fa ${cmd.icon} command-icon"></i>
                                                ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        }
                    }).join("")}
                        </ul>
                    </div>`;
        } else {
            const service = itemData;
            const commandList = [{
                icon: "fa-toggle-on",
                cmd: 6,
                text: "Disable active checks",
                condition: service.active_checks_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 5,
                text: "Enable active checks of this service",
                condition: service.active_checks_enabled === '0'
            },
            {
                icon: "fa-clock-o",
                cmd: 7,
                text: "Re-schedule next check"
            },
            {
                icon: "fa-external-link-square",
                cmd: 30,
                text: "Submit passive check result",
                condition: service.passive_checks_enabled === '1'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 40,
                text: "Stop accepting passive checks",
                condition: service.passive_checks_enabled === '1'
            },
            {
                icon: "fa-play-circle-o",
                cmd: 39,
                text: "Start accepting passive checks",
                condition: service.passive_checks_enabled === '0'
            },
            {
                icon: "fa-stop-circle-o",
                cmd: 100,
                text: "Stop obsessing over service",
                condition: service.obsess === '1'
            },
            {
                icon: "fa-play-circle-o",
                cmd: 99,
                text: "Start obsessing over this service",
                condition: service.obsess === '0'
            },
            {
                icon: "fa-gavel",
                cmd: 34,
                text: "Acknowledge this service problem",
                condition: service.current_state != '0' && service.problem_has_been_acknowledged === '0'
            },
            {
                icon: "fa-gavel",
                cmd: 52,
                text: "Remove problem acknowledgement",
                condition: service.problem_has_been_acknowledged === '1'
            },
            {
                icon: "fa-microphone",
                cmd: 23,
                text: "Disable notifications",
                condition: service.notifications_enabled === '1'
            },
            {
                icon: "fa-microphone-slash",
                cmd: 22,
                text: "Enable notifications for this service",
                condition: service.notifications_enabled === '0'
            },
            {
                icon: "fa-bullhorn",
                cmd: 160,
                text: "Send custom notification"
            },
            {
                icon: "fa-moon-o",
                cmd: 56,
                text: "Schedule downtime"
            },
            {
                icon: "fa-toggle-on",
                cmd: 46,
                text: "Disable event handler",
                condition: service.event_handler_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 45,
                text: "Enable event handler",
                condition: service.event_handler_enabled === '0'
            },
            {
                icon: "fa-toggle-on",
                cmd: 60,
                text: "Disable flap detection",
                condition: service.flap_detection_enabled === '1'
            },
            {
                icon: "fa-toggle-off",
                cmd: 59,
                text: "Enable flap detection",
                condition: service.flap_detection_enabled === '0'
            },
            {
                icon: "fa-eraser",
                cmd: 174,
                text: "Clear flapping state",
                condition: service.flap_detection_enabled === '1'
            },
            {
                icon: "fa-trash",
                cmd: 9999, //random
                text: "Delete Service"
            }
            ];

            return `
                <div class="commands-card">
                    <h5 class="card-title">Service Commands</h5>
                    <ul class="command-list">
                        ${commandList
                    .filter(cmd => cmd.condition !== undefined ? cmd.condition : true)
                    .map(cmd => {
                        if (cmd.cmd === 9999) {
                            hostip = urlParams.get('hostip');
                            return `
                                        <a href="#" class="command-link" onclick="callDeleteService('${hostip}', '${service.service_description}'); return false;">
                                            <li class="command-item">
                                                    <i class="fa ${cmd.icon} command-icon"></i>
                                                    ${cmd.text}
                                            </li>
                                        </a>
                                    `;
                        } else {
                            return `
                                        <a href="#" class="command-link" onclick="openModalWithIframe('${baseUrl}?cmd_typ=${cmd.cmd}&host=${hostname}&service=${service.service_description}', '${cmd.text}'); return false;">
                                            <li class="command-item">
                                                    <i class="fa ${cmd.icon} command-icon"></i>
                                                    ${cmd.text}
                                            </li>
                                         </a>
                                    `;
                        }
                    }).join("")}
                    </ul>
                </div>`;
        }
    } catch (error) {
        console.error('Error generating context menu:', error);
        return `<div style="background: #fff; border-radius: 8px; padding: 12px; width: 260px; font-family: Arial, sans-serif; color: #ff4444;">Error loading commands</div>`;
    }
}

const handleContextMenu = async (e, triggerType) => {
    const modalBody = document.getElementById('modal-body');
    const modalTitle = document.getElementById('modal-title');
    const contextMenu = document.getElementById('custom-context-menu');

    const showContextMenu = (content, e) => {
        e.preventDefault();
        if (triggerType === 'click') {
            e.stopPropagation();
        }
        contextMenu.innerHTML = content;

        // Position adjustments
        contextMenu.style.display = 'block';
        contextMenu.style.visibility = 'hidden';
        contextMenu.style.left = '-9999px';
        contextMenu.style.top = '0px';

        const menuWidth = contextMenu.offsetWidth;
        const menuHeight = contextMenu.offsetHeight;

        contextMenu.style.display = 'none';
        contextMenu.style.visibility = 'visible';

        let adjustedX, adjustedY;

        if (triggerType === 'contextmenu') {
            // Include current page scroll offsets so the menu is positioned correctly
            const scrollX = window.scrollX || window.pageXOffset;
            const scrollY = window.scrollY || window.pageYOffset;

            // Initial position based on click coordinates plus scroll offset
            adjustedX = e.clientX + scrollX;
            adjustedY = e.clientY + scrollY;

            // Calculate viewport bounds (including scroll)
            const viewportRight = scrollX + window.innerWidth;
            const viewportBottom = scrollY + window.innerHeight;

            // Ensure the menu stays within the visible viewport
            if (adjustedX + menuWidth > viewportRight) adjustedX = viewportRight - menuWidth;
            if (adjustedY + menuHeight > viewportBottom) adjustedY = viewportBottom - menuHeight;

            // Prevent negative positioning
            adjustedX = Math.max(adjustedX, scrollX);
            adjustedY = Math.max(adjustedY, scrollY);
        } else {
            const modalContent = document.querySelector('.modal-content');
            const modalRect = modalContent.getBoundingClientRect();
            const optionsButton = document.getElementById('modal-options');
            const buttonRect = optionsButton.getBoundingClientRect();
            const scrollY = window.scrollY || window.pageYOffset; // Get vertical scroll offset

            adjustedX = buttonRect.right - menuWidth;
            adjustedY = buttonRect.bottom + 5 + scrollY; // Add scroll offset to Y position

            // Adjust X to stay within modal bounds
            if (adjustedX < modalRect.left) {
                adjustedX = modalRect.left + 5;
            } else if (adjustedX + menuWidth > modalRect.right) {
                adjustedX = modalRect.right - menuWidth - 5;
            }

            // Adjust Y if menu exceeds window height
            if (adjustedY + menuHeight > window.innerHeight + scrollY) {
                adjustedY = buttonRect.top - menuHeight - 5 + scrollY; // Position above button
            }

            // Ensure Y doesn't go above the top of the modal or viewport
            if (adjustedY < scrollY) {
                adjustedY = modalRect.top + 5 + scrollY;
            }
        }

        contextMenu.style.left = `${adjustedX}px`;
        contextMenu.style.top = `${adjustedY}px`;
        contextMenu.style.display = 'block';

        const closeMenu = (event) => {
            if (!contextMenu.contains(event.target)) {
                contextMenu.style.display = 'none';
                document.removeEventListener('click', closeMenu);
            }
        };
        document.addEventListener('click', closeMenu);
    };

    let content;
    if (modalBody.textContent.includes('Host Name')) {
        const hostNameMatch = modalBody.textContent.match(/Host Name:\s*([^\s]+)/);
        const hostname = hostNameMatch ? hostNameMatch[1] : '';
        content = `
            <div class="commands-card">
                <h5 class="card-title">Advanced Host Commands</h5>
                <ul class="command-list">
                    <a href="#" class="command-link" onclick="openCommandModal('checkStatusFormHost.php?hostname=${encodeURIComponent(hostname)}', 'Check Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-cog command-icon"></i>
                            Check Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('alarmSettingsHost.php?hostname=${encodeURIComponent(hostname)}', 'Alarm Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-bell command-icon"></i>
                            Alarm Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('checkCommandHost.php?hostname=${encodeURIComponent(hostname)}', 'Command Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-terminal command-icon"></i>
                            Command Settings
                        </li>
                    </a>
                </ul>
            </div>
        `;
    } else if (modalBody.textContent.includes('Status Information:')) {
        const serviceName = modalTitle.textContent;
        content = `
            <div class="commands-card">
                <h5 class="card-title">Advanced Service Commands</h5>
                <ul class="command-list">
                    <a href="#" class="command-link" onclick="openCommandModal('checkStatusFormService.php?servicename=${encodeURIComponent(serviceName)}&hostname=${encodeURIComponent(realHostName)}', 'Check Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-cog command-icon"></i>
                            Check Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('alarmSettingsService.php?servicename=${encodeURIComponent(serviceName)}&hostname=${encodeURIComponent(realHostName)}', 'Alarm Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-bell command-icon"></i>
                            Alarm Settings
                        </li>
                    </a>
                    <a href="#" class="command-link" onclick="openCommandModal('checkCommandService.php?servicename=${encodeURIComponent(serviceName)}&hostname=${encodeURIComponent(realHostName)}', 'Command Settings'); return false;">
                        <li class="command-item">
                            <i class="fa fa-terminal command-icon"></i>
                            Command Settings
                        </li>
                    </a>
                </ul>
            </div>
        `;
    }

    if (content) {
        showContextMenu(content, e);
    }
};

// Attach event listeners
document.getElementById('modal-body').addEventListener('contextmenu', (e) => handleContextMenu(e, 'contextmenu'));
document.getElementById('modal-options').addEventListener('click', (e) => handleContextMenu(e, 'click'));
document.addEventListener('click', () => {
    document.getElementById('custom-context-menu').style.display = 'none';
});