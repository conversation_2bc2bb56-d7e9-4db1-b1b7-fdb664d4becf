(function(){
    // Inject Export PDF button once DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        const controls = document.querySelector('#availability-container .availability-controls');
        if (controls && !document.getElementById('availability-export')) {
            const btn = document.createElement('button');
            btn.id = 'availability-export';
            btn.className = 'availability-export';
            btn.title = 'Export availability & trends as PDF';
            btn.innerHTML = '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>';
            controls.appendChild(btn);

            btn.addEventListener('click', () => {
                exportPdf(btn);
            });
        }
    });

    // Keep track of current host name (populated from hostLoaded event)
    let currentHost = null;
    document.addEventListener('hostLoaded', e => {
        currentHost = e.detail.hostname;
    });

    // ---------------- PDF generation -----------------
    async function exportPdf(buttonEl){
        try {
            if (!currentHost){
                alert('Please load a host first');
                return;
            }
            // Verify jsPDF has been loaded
            if (!window.jspdf || !window.jspdf.jsPDF){
                alert('PDF library failed to load');
                return;
            }

            const startInput = document.getElementById('availability-start');
            const endInput   = document.getElementById('availability-end');
            const startTs = Math.floor(new Date(startInput.value).getTime()/1000);
            const endTs   = Math.floor(new Date(endInput.value).getTime()/1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs){
                alert('Invalid date range');
                return;
            }

            // Disable button + spinner
            const originalHtml = buttonEl.innerHTML;
            buttonEl.disabled = true;
            buttonEl.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

            // Build URLs
            const hostAvailUrl  = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=${encodeURIComponent(currentHost)}&starttime=${startTs}&endtime=${endTs}`;
            const svcAvailUrl   = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services&hostname=${encodeURIComponent(currentHost)}&starttime=${startTs}&endtime=${endTs}`;
            const timelineUrl   = `/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=host&hostname=${encodeURIComponent(currentHost)}&starttime=${startTs}&endtime=${endTs}`;

            // Fetch data concurrently
            const [hostJs, svcJs, tlJs] = await Promise.all([
                fetch(hostAvailUrl, { credentials: 'include' }).then(r=>r.json()).catch(()=>null),
                fetch(svcAvailUrl, { credentials: 'include' }).then(r=>r.json()).catch(()=>null),
                fetch(timelineUrl, { credentials: 'include' }).then(r=>r.json()).catch(()=>null)
            ]);

            // Remove hostStatusJson fetch block and currentHostState
            const hostIp = (new URLSearchParams(window.location.search)).get('hostip') || (new URLSearchParams(window.location.search)).get('ip') || '';

            await generatePdf(hostJs, svcJs, tlJs, startTs, endTs, hostIp);
        } catch (err){
            console.error('PDF export error', err);
            alert('Error generating PDF');
        } finally {
            if (buttonEl){
                buttonEl.disabled = false;
                buttonEl.innerHTML = '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>';
            }
        }
    }

    function stateLabel(val,isHost){
        if(val===undefined||val===null||val==='') return '';
        const num=parseInt(val,10);
        if(isNaN(num)) return String(val).toUpperCase();

        if(isHost){
            // Host state bitmask mapping used elsewhere in codebase: 1=pending,2=up,4=down,8=unreachable
            if(num & 2) return 'UP';
            if(num & 4) return 'DOWN';
            if(num & 8) return 'UNREACHABLE';
            if(num & 1) return 'PENDING';
        } else {
            // Service state bitmask mapping: 1=pending,2=ok,4=warning,8=unknown,16=critical
            if(num & 2) return 'OK';
            if(num & 4) return 'WARNING';
            if(num & 16) return 'CRITICAL';
            if(num & 8) return 'UNKNOWN';
            if(num & 1) return 'PENDING';
        }
        // Fallback for simple numeric states (0,1,2,3)
        if(isHost){
            return ['UP','DOWN','UNREACHABLE'][num] || String(num);
        } else {
            return ['OK','WARNING','CRITICAL','UNKNOWN'][num] || String(num);
        }
    }

    async function generatePdf(hostJs, svcJs, tlJs, startTs, endTs,hostIp){
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({ orientation: 'landscape' });

        const startDate = new Date(startTs*1000);
        const endDate   = new Date(endTs*1000);
        const rangeDays = Math.ceil((endDate - startDate) / (1000*60*60*24));
        const titleRangeTxt = rangeDays === 1 ? 'Last 24 Hours' : `Last ${rangeDays} Days`;

        doc.setProperties({
            title: `Blesk Availability Report (${titleRangeTxt})`,
            author: 'Blesk',
            creator: 'Blesk'
        });

        const pageMargin = 20;
        const headingY   = 20; // header top line
        const contentStartY = headingY + 32; // safe distance below header
        const pageWidth  = doc.internal.pageSize.getWidth();
        const ipRegex=/^(?:\d{1,3}\.){3}\d{1,3}$/;
        const hostDisplay=ipRegex.test(currentHost)?currentHost:(hostIp?`${currentHost} (${hostIp})`:currentHost);

        function drawHeaderFooter(pageNumber, totalPages){
            // Logo (optional)
            try {
                doc.addImage('/images/logoblesk.png', 'PNG', 10, 6, 12, 12);
            } catch(e) {/* ignore */}

            doc.setFontSize(14);
            doc.setTextColor(0,0,0);
            const titleText = `${hostDisplay} Availability Report (${titleRangeTxt})`;
            doc.text(titleText, pageWidth/2, headingY, { align: 'center' });

            doc.setFontSize(10);
            const rangeText = `Range: ${startDate.toLocaleString()} - ${endDate.toLocaleString()}`;
            doc.text(rangeText, pageWidth/2, headingY + 6, { align: 'center' });

            doc.setFontSize(8);
            doc.text(`Page ${pageNumber} of ${totalPages}`, pageWidth - pageMargin, doc.internal.pageSize.getHeight() - 10, { align: 'right' });
        }

        // -------- Host availability summary --------
        const hostData = hostJs?.data?.host;
        if (hostData){
            const up  = (+hostData.time_up || 0) + (+hostData.scheduled_time_up || 0);
            const down= (+hostData.time_down || 0) + (+hostData.scheduled_time_down || 0);
            const unreach = (+hostData.time_unreachable || 0) + (+hostData.scheduled_time_unreachable || 0);
            const total = up + down + unreach;
            const pct = v => total ? ((v/total)*100).toFixed(1) : '0.0';

            doc.autoTable({
                head: [[ 'Host', 'Up %', 'Down %', 'Unreach %' ]],
                body: [[ hostDisplay, pct(up), pct(down), pct(unreach) ]],
                startY: contentStartY,
                margin: { top: contentStartY, left: pageMargin, right: pageMargin },
                theme: 'grid',
                styles: { fontSize: 9, cellPadding: 2 },
                headStyles: { fillColor: [245,245,245], textColor: [51,51,51] }
            });
        }

        // -------- Host state changes (timeline + table) --------
        const rawTimelineArr = (tlJs?.data?.statechangelist || []);
        
        // Filter out synthetic pseudo-state entries from Nagios API
        const timelineArr = rawTimelineArr
            .filter(entry => !entry.plugin_output || (!entry.plugin_output.includes('Pseudo-State')))
            .sort((a,b)=>a.timestamp-b.timestamp);
        if (timelineArr.length){
            let curY = doc.lastAutoTable.finalY + 12;

            // Timeline label
            doc.setFontSize(10);
            doc.text('State Timeline', pageMargin, curY);
            curY += 4;

            // Draw timeline bar
            const barHeight = 8;
            const barWidth  = pageWidth - 2*pageMargin;
            const startMs = startTs*1000;
            const endMs   = endTs*1000;
            const duration = endMs - startMs;
            const colorMap = {
                UP:[165,214,167],up:[165,214,167],2:[165,214,167],0:[165,214,167],
                DOWN:[229,115,115],down:[229,115,115],4:[229,115,115],1:[229,115,115],
                UNREACHABLE:[169,182,201],unreachable:[169,182,201],8:[169,182,201]
            };

            // Draw timeline background
            doc.setFillColor(245, 245, 245);
            doc.rect(pageMargin, curY, barWidth, barHeight, 'F');
            doc.setDrawColor(220, 220, 220);
            doc.setLineWidth(0.5);
            doc.rect(pageMargin, curY, barWidth, barHeight, 'D');
            
            // Draw timeline segments - always start from beginning of range
            for(let i=0;i<timelineArr.length;i++){
                const cur = timelineArr[i];
                const segStart = i === 0 ? startMs : cur.timestamp; // First segment starts at range start
                const segEnd = (i < timelineArr.length-1) ? timelineArr[i+1].timestamp : endMs;
                
                // Clip to range
                const s = Math.max(segStart, startMs);
                const e = Math.min(segEnd, endMs);
                if (e<=s) continue;
                
                const x = pageMargin + ((s-startMs)/duration)*barWidth;
                const w = Math.max(1, ((e-s)/duration)*barWidth);
                const col = colorMap[cur.state] || [200,200,200];
                
                // Draw segment with gradient effect
                doc.setFillColor(...col);
                doc.rect(x, curY + 1, w, barHeight - 2, 'F');
                
                // Add subtle highlight on top
                const lighterCol = col.map(c => Math.min(255, c + 20));
                doc.setFillColor(...lighterCol);
                doc.rect(x, curY + 1, w, 2, 'F');
                
                // Add segment borders for definition
                if (w > 2) {
                    doc.setDrawColor(0, 0, 0);
                    doc.setLineWidth(0.2);
                    doc.line(x, curY + 1, x, curY + barHeight - 1);
                    doc.line(x + w, curY + 1, x + w, curY + barHeight - 1);
                }
            }

            curY += barHeight + 6;



            // Table of changes
            doc.autoTable({
                head: [[ 'Timestamp', 'State' ]],
                body: timelineArr.map(sc => {
                    const stTxt = sc.state_text ? sc.state_text.toUpperCase() : stateLabel(sc.state,true);
                    return [ new Date(sc.timestamp).toLocaleString(), stTxt ];
                }),
                startY: curY,
                margin: { top: contentStartY, left: pageMargin, right: pageMargin },
                theme: 'striped',
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [245,245,245], textColor: [51,51,51] }
            });
        }

        // -------- Service availability summary --------
        const services = svcJs?.data?.services || [];
        if (services.length){
            // Add new page for clarity
            doc.addPage();

            const bodyRowsRaw = services.map(svc => {
                const total = (+svc.time_ok || 0) + (+svc.time_warning || 0) + (+svc.time_critical || 0) + (+svc.time_unknown || 0);
                const pct = v => total ? ((v/total)*100).toFixed(1) : '0.0';
                return [ svc.description, pct(+svc.time_ok), pct(+svc.time_warning), pct(+svc.time_critical), pct(+svc.time_unknown) ];
            });

            const hdrs = [ 'Service', 'OK %', 'Warn %', 'Crit %', 'Unk %' ];

            // Colour highlight like reports.js
            const bodyRows = bodyRowsRaw.map(row => row.map((cell, idx) => {
                if (idx === 0) return cell; // Service name no highlight
                const num = parseFloat(cell);
                if (isNaN(num) || num === 0) return cell;
                let fill = null;
                if (idx === 1) fill = [165,214,167]; // OK
                else if (idx === 2) fill = [255,224,130]; // Warn
                else if (idx === 3) fill = [229,115,115]; // Crit
                else if (idx === 4) fill = [169,182,201]; // Unk
                return { content: cell, styles: { fillColor: fill, textColor: [51,51,51] } };
            }));

            // Insert table title row
            bodyRows.unshift([{ content: 'Service Availability Summary', colSpan: hdrs.length, styles: { fontStyle: 'bold', halign: 'left', fillColor: [230,230,230], textColor:[51,51,51] } }]);

            doc.autoTable({
                head: [hdrs],
                body: bodyRows,
                startY: contentStartY,
                margin: { top: contentStartY, left: pageMargin, right: pageMargin },
                theme: 'grid',
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [245,245,245], textColor: [51,51,51] }
            });
        }

        // -------- Service state change timelines --------
        if (services.length){
            // Fetch timelines concurrently
            const svcPromises = services.map(svc=>{
                const url=`/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=service&hostname=${encodeURIComponent(currentHost)}&servicedescription=${encodeURIComponent(svc.description)}&starttime=${startTs}&endtime=${endTs}`;
                return fetch(url,{credentials:'include'}).then(r=>r.json()).then(js=>{
                    const rawList = js?.data?.statechangelist || [];
                    const filteredList = rawList.filter(entry => !entry.plugin_output || (!entry.plugin_output.includes('Pseudo-State')));
                    return {name:svc.description, list:filteredList.sort((a,b)=>a.timestamp-b.timestamp)};
                }).catch(()=>({name:svc.description,list:[]}));
            });

            const svcTimelines = await Promise.all(svcPromises);

            const svcColorMap={
                OK:[165,214,167],ok:[165,214,167],2:[165,214,167],0:[165,214,167],
                WARNING:[255,224,130],warning:[255,224,130],4:[255,224,130],1:[255,224,130],
                CRITICAL:[229,115,115],critical:[229,115,115],16:[229,115,115],2.5:0,
                UNKNOWN:[169,182,201],unknown:[169,182,201],8:[169,182,201],3:[169,182,201]
            };

            for(const svcT of svcTimelines){
                if(!svcT.list.length) continue;

                doc.addPage();
                let curY = contentStartY;
                doc.setFontSize(11);
                doc.text(`Service Trends – ${svcT.name}`, pageMargin, curY);
                curY += 6;

                // Draw timeline bar
                const barHeight=8; const barWidth=pageWidth-2*pageMargin; const startMs=startTs*1000; const endMs=endTs*1000; const duration=endMs-startMs;
                
                // Draw timeline background
                doc.setFillColor(245, 245, 245);
                doc.rect(pageMargin, curY, barWidth, barHeight, 'F');
                doc.setDrawColor(220, 220, 220);
                doc.setLineWidth(0.5);
                doc.rect(pageMargin, curY, barWidth, barHeight, 'D');
                
                // Draw timeline segments - always start from beginning of range
                svcT.list.forEach((ev,idx)=>{
                    const segStart = idx === 0 ? startMs : ev.timestamp; // First segment starts at range start
                    const segEnd = (idx < svcT.list.length-1) ? svcT.list[idx+1].timestamp : endMs;
                    const s = Math.max(segStart, startMs); 
                    const e = Math.min(segEnd, endMs); 
                    if(e<=s) return;
                    const x = pageMargin + ((s-startMs)/duration)*barWidth; 
                    const w = Math.max(1, ((e-s)/duration)*barWidth);
                    const col = svcColorMap[ev.state] || [200,200,200]; 
                    
                    // Draw segment with gradient effect
                    doc.setFillColor(...col); 
                    doc.rect(x, curY + 1, w, barHeight - 2, 'F');
                    
                    // Add subtle highlight on top
                    const lighterCol = col.map(c => Math.min(255, c + 20));
                    doc.setFillColor(...lighterCol);
                    doc.rect(x, curY + 1, w, 2, 'F');
                    
                    // Add segment borders for definition
                    if (w > 2) {
                        doc.setDrawColor(0, 0, 0);
                        doc.setLineWidth(0.2);
                        doc.line(x, curY + 1, x, curY + barHeight - 1);
                        doc.line(x + w, curY + 1, x + w, curY + barHeight - 1);
                    }
                });
                curY += barHeight + 6;

                // Table of changes
                doc.autoTable({
                    head:[[ 'Timestamp','State' ]],
                    body: svcT.list.map(ev=>{
                        const stTxt = ev.state_text ? ev.state_text.toUpperCase() : stateLabel(ev.state,false);
                        return [ new Date(ev.timestamp).toLocaleString(), stTxt ];
                    }),
                    startY: curY,
                    margin:{ top: contentStartY, left: pageMargin, right: pageMargin },
                    theme:'striped',
                    styles:{ fontSize:8, cellPadding:2 },
                    headStyles:{ fillColor:[245,245,245], textColor:[51,51,51] }
                });
            }
        }

        // Draw header/footer on every page
        const totalPages = doc.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++){
            doc.setPage(i);
            drawHeaderFooter(i, totalPages);
        }

        const ts = new Date().toISOString().replace(/[:T]/g,'-').split('.')[0];
        doc.save(`availability_report_${currentHost}_${ts}.pdf`);
    }
})(); 