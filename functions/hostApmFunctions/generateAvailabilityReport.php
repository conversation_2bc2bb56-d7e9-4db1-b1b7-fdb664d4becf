<?php
// generateAvailabilityReport.php - generates host-specific availability reports
// Expected POST params: hostname, startTs, endTs, hostIp (optional)

header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Load environment variables (DB/Nagios credentials)
require_once dirname(__DIR__, 2) . '/loadenv.php';

// ------------------------- Helper functions (shared with sendScheduledReport.php) -------------------------------
/**
 * Get DB connection to 'blesk' for retrieving user credentials
 */
function getDatabaseConnectionAdminUser(): mysqli {
    $conn = new mysqli($_ENV['DB_SERVER'], $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], 'blesk');
    if ($conn->connect_error) {
        die('DB connection failed: ' . $conn->connect_error);
    }
    return $conn;
}

/**
 * Fetch Nagios HTTP basic auth credentials (user_id=1)
 */
function getUserCredentials(): ?array {
    $conn = getDatabaseConnectionAdminUser();
    $result = $conn->query("SELECT username, password FROM users WHERE user_id = 1 LIMIT 1");
    $cred = null;
    if ($result && $row = $result->fetch_assoc()) {
        $cred = ['user' => $row['username'], 'pass' => $row['password']];
    }
    $conn->close();
    return $cred;
}

/**
 * Determine self IP address (stored by bubblemaps)
 */
function getSelfIp(): string {
    $ip = trim(@file_get_contents('/etc/sysconfig/ipaddr'));
    if (!$ip) die("Unable to determine self IP");
    return $ip;
}

// -----------------------------------------------------------------------------
// Configuration / constants
// -----------------------------------------------------------------------------
$FPDF_PATH     = __DIR__ . '/../reportsFunctions/fpdf.php'; // FPDF library path
$NAGIOS_BASE   = 'https://' . getSelfIp(); // Use real host/IP instead of localhost
$TMP_DIR       = sys_get_temp_dir();

// -----------------------------------------------------------------------------
// Get POST parameters
// -----------------------------------------------------------------------------
$hostname = isset($_POST['hostname']) ? trim($_POST['hostname']) : '';
$startTs = isset($_POST['startTs']) ? intval($_POST['startTs']) : 0;
$endTs = isset($_POST['endTs']) ? intval($_POST['endTs']) : 0;
$hostIp = isset($_POST['hostIp']) ? trim($_POST['hostIp']) : '';

// Validate parameters
if (empty($hostname)) {
    echo json_encode(['success' => false, 'message' => 'Hostname is required']);
    exit;
}

if ($startTs <= 0 || $endTs <= 0 || $endTs <= $startTs) {
    echo json_encode(['success' => false, 'message' => 'Invalid time range']);
    exit;
}

// -----------------------------------------------------------------------------
// Helper to fetch Nagios availability data (same as sendScheduledReport.php)
// -----------------------------------------------------------------------------
function fetchJson(string $path): array
{
    $url = $GLOBALS['NAGIOS_BASE'] . $path;

    static $creds = null;
    if ($creds === null) $creds = getUserCredentials();

    // Initialise cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    // Allow self-signed certificates (internal Nagios)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Pass basic-auth credentials if available
    if ($creds) {
        curl_setopt($ch, CURLOPT_USERPWD, $creds['user'] . ':' . $creds['pass']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    }

    $raw = curl_exec($ch);
    if ($raw === false) {
        error_log('[generateAvailabilityReport] cURL error: ' . curl_error($ch));
        curl_close($ch);
        return [];
    }

    $http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($http !== 200) {
        error_log('[generateAvailabilityReport] HTTP ' . $http . ' fetching ' . $url);
        return [];
    }

    $json = json_decode($raw, true);
    if (!is_array($json)) return [];
    return $json['data'] ?? [];
}

// -----------------------------------------------------------------------------
// State label helper function (replicate JavaScript stateLabel function)
// -----------------------------------------------------------------------------
function stateLabel($val, $isHost) {
    if ($val === null || $val === '') return '';
    $num = intval($val);

    if ($isHost) {
        // Host state bitmask mapping: 1=pending,2=up,4=down,8=unreachable
        if ($num & 2) return 'UP';
        if ($num & 4) return 'DOWN';
        if ($num & 8) return 'UNREACHABLE';
        if ($num & 1) return 'PENDING';
        // Fallback for simple numeric states (0,1,2,3)
        $states = ['UP', 'DOWN', 'UNREACHABLE'];
        return $states[$num] ?? strval($num);
    } else {
        // Service state bitmask mapping: 1=pending,2=ok,4=warning,8=unknown,16=critical
        if ($num & 2) return 'OK';
        if ($num & 4) return 'WARNING';
        if ($num & 16) return 'CRITICAL';
        if ($num & 8) return 'UNKNOWN';
        if ($num & 1) return 'PENDING';
        // Fallback for simple numeric states (0,1,2,3)
        $states = ['OK', 'WARNING', 'CRITICAL', 'UNKNOWN'];
        return $states[$num] ?? strval($num);
    }
}

// -----------------------------------------------------------------------------
// Fetch data from Nagios APIs (exactly like JavaScript version)
// -----------------------------------------------------------------------------
try {
    // Build URLs exactly like JavaScript
    $hostAvailUrl = "/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=" . urlencode($hostname) . "&starttime={$startTs}&endtime={$endTs}";
    $svcAvailUrl = "/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services&hostname=" . urlencode($hostname) . "&starttime={$startTs}&endtime={$endTs}";
    $timelineUrl = "/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=host&hostname=" . urlencode($hostname) . "&starttime={$startTs}&endtime={$endTs}";

    // Fetch data concurrently (simulate Promise.all)
    $hostJs = fetchJson($hostAvailUrl);
    $svcJs = fetchJson($svcAvailUrl);
    $tlJs = fetchJson($timelineUrl);

    // Filter timeline data exactly like JavaScript (remove pseudo-state entries)
    $rawTimelineArr = $tlJs['statechangelist'] ?? [];
    $timelineArr = array_filter($rawTimelineArr, function($entry) {
        return !isset($entry['plugin_output']) || !str_contains($entry['plugin_output'], 'Pseudo-State');
    });

    // Sort by timestamp
    usort($timelineArr, function($a, $b) {
        return $a['timestamp'] - $b['timestamp'];
    });

    // Extract data exactly like JavaScript
    $hostData = $hostJs['host'] ?? null;
    $services = $svcJs['services'] ?? [];

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error fetching data: ' . $e->getMessage()]);
    exit;
}

// -----------------------------------------------------------------------------
// Generate PDF report using FPDF
// -----------------------------------------------------------------------------
$fpdfAvailable = false;
if (!class_exists('FPDF') && file_exists($FPDF_PATH)) {
    require_once $FPDF_PATH;
}
if (class_exists('FPDF')) {
    $fpdfAvailable = true;
}

if (!$fpdfAvailable) {
    echo json_encode(['success' => false, 'message' => 'FPDF library not available']);
    exit;
}

// After FPDF is available, define subclass for header/footer (exactly like JavaScript version)
if (!class_exists('BleskAvailabilityPDF')) {
    class BleskAvailabilityPDF extends FPDF {
        public string $titleText = '';
        public string $rangeText = '';
        public string $logoPath = '';
        public string $hostDisplay = '';
        public int $pageMargin = 20;
        public int $headingY = 20;

        function Header() {
            // Logo (optional) - exactly like JavaScript
            if ($this->logoPath && file_exists($this->logoPath)) {
                $this->Image($this->logoPath, 10, 6, 12, 12);
            }

            // Title - exactly like JavaScript
            $this->SetFont('Arial', '', 14);
            $this->SetTextColor(0, 0, 0);
            $this->SetXY(0, $this->headingY);
            $this->Cell(0, 0, $this->titleText, 0, 1, 'C');

            // Range text - exactly like JavaScript
            $this->SetFont('Arial', '', 10);
            $this->SetXY(0, $this->headingY + 6);
            $this->Cell(0, 0, $this->rangeText, 0, 1, 'C');

            // Page number - exactly like JavaScript
            $this->SetFont('Arial', '', 8);
            $pageText = 'Page ' . $this->PageNo() . ' of {nb}';
            $this->SetXY($this->GetPageWidth() - $this->pageMargin - 30, $this->GetPageHeight() - 10);
            $this->Cell(30, 0, $pageText, 0, 0, 'R');
        }

        function Footer() {
            // Footer is handled in Header for consistency with JavaScript
        }

        // Helper to get content start Y position
        public function getContentStartY(): int {
            return $this->headingY + 32;
        }
    }
}

// Calculate date range information exactly like JavaScript
$rangeDays = ceil(($endTs - $startTs) / 86400);
$titleRangeTxt = $rangeDays === 1 ? 'Last 24 Hours' : ('Last ' . $rangeDays . ' Days');

// Determine host display name exactly like JavaScript
$ipRegex = '/^(?:\d{1,3}\.){3}\d{1,3}$/';
$hostDisplay = preg_match($ipRegex, $hostname) ? $hostname : ($hostIp ? "{$hostname} ({$hostIp})" : $hostname);

// Create PDF instance exactly like JavaScript (landscape orientation)
$pdf = new BleskAvailabilityPDF('L', 'mm', 'A4');
$pdf->AliasNbPages();
$pdf->SetAuthor('Blesk');
$pdf->titleText = "{$hostDisplay} Availability Report ({$titleRangeTxt})";
$pdf->rangeText = 'Range: ' . date('Y-m-d H:i:s', $startTs) . ' - ' . date('Y-m-d H:i:s', $endTs);
$pdf->hostDisplay = $hostDisplay;

// Use logo path (try to find the logo)
$logoPath = __DIR__ . '/../../images/logoblesk.png';
if (file_exists($logoPath)) {
    $pdf->logoPath = $logoPath;
}

$pdf->SetTitle($pdf->titleText);
$pdf->AddPage();

$contentStartY = $pdf->getContentStartY();
$pageWidth = $pdf->GetPageWidth();
$pageMargin = $pdf->pageMargin;

// -----------------------------------------------------------------------------
// Host availability summary (exactly like JavaScript version)
// -----------------------------------------------------------------------------
if ($hostData) {
    $up = ($hostData['time_up'] ?? 0) + ($hostData['scheduled_time_up'] ?? 0);
    $down = ($hostData['time_down'] ?? 0) + ($hostData['scheduled_time_down'] ?? 0);
    $unreach = ($hostData['time_unreachable'] ?? 0) + ($hostData['scheduled_time_unreachable'] ?? 0);
    $total = $up + $down + $unreach;

    $pct = function($v) use ($total) {
        return $total ? number_format(($v / $total) * 100, 1) : '0.0';
    };

    // Create table exactly like JavaScript autoTable
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetFillColor(245, 245, 245);
    $pdf->SetTextColor(51, 51, 51);
    $pdf->SetY($contentStartY);

    // Table headers
    $headers = ['Host', 'Up %', 'Down %', 'Unreach %'];
    $colWidth = ($pageWidth - 2 * $pageMargin) / 4;

    foreach ($headers as $header) {
        $pdf->Cell($colWidth, 7, $header, 1, 0, 'C', true);
    }
    $pdf->Ln();

    // Table data
    $pdf->SetFont('Arial', '', 9);
    $pdf->SetFillColor(255, 255, 255);
    $pdf->Cell($colWidth, 6, $hostDisplay, 1, 0, 'L');
    $pdf->Cell($colWidth, 6, $pct($up), 1, 0, 'C');
    $pdf->Cell($colWidth, 6, $pct($down), 1, 0, 'C');
    $pdf->Cell($colWidth, 6, $pct($unreach), 1, 0, 'C');
    $pdf->Ln();
}

// -----------------------------------------------------------------------------
// Host state changes (timeline + table) - exactly like JavaScript version
// -----------------------------------------------------------------------------
if (!empty($timelineArr)) {
    $curY = $pdf->GetY() + 12;

    // Timeline label
    $pdf->SetFont('Arial', '', 10);
    $pdf->SetXY($pageMargin, $curY);
    $pdf->Cell(0, 0, 'State Timeline');
    $curY += 4;

    // Draw timeline bar exactly like JavaScript
    $barHeight = 8;
    $barWidth = $pageWidth - 2 * $pageMargin;
    $startMs = $startTs * 1000;
    $endMs = $endTs * 1000;
    $duration = $endMs - $startMs;

    // Color map exactly like JavaScript
    $colorMap = [
        'UP' => [165, 214, 167], 'up' => [165, 214, 167], '2' => [165, 214, 167], '0' => [165, 214, 167],
        'DOWN' => [229, 115, 115], 'down' => [229, 115, 115], '4' => [229, 115, 115], '1' => [229, 115, 115],
        'UNREACHABLE' => [169, 182, 201], 'unreachable' => [169, 182, 201], '8' => [169, 182, 201]
    ];

    // Draw timeline background
    $pdf->SetFillColor(245, 245, 245);
    $pdf->Rect($pageMargin, $curY, $barWidth, $barHeight, 'F');
    $pdf->SetDrawColor(220, 220, 220);
    $pdf->SetLineWidth(0.5);
    $pdf->Rect($pageMargin, $curY, $barWidth, $barHeight, 'D');

    // Draw timeline segments exactly like JavaScript
    for ($i = 0; $i < count($timelineArr); $i++) {
        $cur = $timelineArr[$i];
        $segStart = $i === 0 ? $startMs : $cur['timestamp'];
        $segEnd = ($i < count($timelineArr) - 1) ? $timelineArr[$i + 1]['timestamp'] : $endMs;

        // Clip to range
        $s = max($segStart, $startMs);
        $e = min($segEnd, $endMs);
        if ($e <= $s) continue;

        $x = $pageMargin + (($s - $startMs) / $duration) * $barWidth;
        $w = max(1, (($e - $s) / $duration) * $barWidth);
        $col = $colorMap[$cur['state']] ?? [200, 200, 200];

        // Draw segment with gradient effect like JavaScript
        $pdf->SetFillColor($col[0], $col[1], $col[2]);
        $pdf->Rect($x, $curY + 1, $w, $barHeight - 2, 'F');

        // Add subtle highlight on top
        $lighterCol = array_map(function($c) { return min(255, $c + 20); }, $col);
        $pdf->SetFillColor($lighterCol[0], $lighterCol[1], $lighterCol[2]);
        $pdf->Rect($x, $curY + 1, $w, 2, 'F');

        // Add segment borders for definition
        if ($w > 2) {
            $pdf->SetDrawColor(0, 0, 0);
            $pdf->SetLineWidth(0.2);
            $pdf->Line($x, $curY + 1, $x, $curY + $barHeight - 1);
            $pdf->Line($x + $w, $curY + 1, $x + $w, $curY + $barHeight - 1);
        }
    }

    $curY += $barHeight + 6;

    // Table of changes exactly like JavaScript
    $pdf->SetY($curY);
    $pdf->SetFont('Arial', 'B', 8);
    $pdf->SetFillColor(245, 245, 245);
    $pdf->SetTextColor(51, 51, 51);

    // Headers
    $timeColWidth = ($pageWidth - 2 * $pageMargin) * 0.7;
    $stateColWidth = ($pageWidth - 2 * $pageMargin) * 0.3;
    $pdf->Cell($timeColWidth, 6, 'Timestamp', 1, 0, 'C', true);
    $pdf->Cell($stateColWidth, 6, 'State', 1, 0, 'C', true);
    $pdf->Ln();

    // Data rows
    $pdf->SetFont('Arial', '', 8);
    $pdf->SetFillColor(255, 255, 255);
    foreach ($timelineArr as $sc) {
        $stTxt = isset($sc['state_text']) ? strtoupper($sc['state_text']) : stateLabel($sc['state'], true);
        $pdf->Cell($timeColWidth, 6, date('Y-m-d H:i:s', $sc['timestamp']), 1, 0, 'L');
        $pdf->Cell($stateColWidth, 6, $stTxt, 1, 0, 'C');
        $pdf->Ln();
    }
}

// -----------------------------------------------------------------------------
// Service availability summary (exactly like JavaScript version)
// -----------------------------------------------------------------------------
if (!empty($services)) {
    // Add new page for clarity like JavaScript
    $pdf->AddPage();

    $bodyRowsRaw = [];
    foreach ($services as $svc) {
        $total = ($svc['time_ok'] ?? 0) + ($svc['time_warning'] ?? 0) + ($svc['time_critical'] ?? 0) + ($svc['time_unknown'] ?? 0);
        $pct = function($v) use ($total) {
            return $total ? number_format(($v / $total) * 100, 1) : '0.0';
        };
        $bodyRowsRaw[] = [
            $svc['description'],
            $pct($svc['time_ok'] ?? 0),
            $pct($svc['time_warning'] ?? 0),
            $pct($svc['time_critical'] ?? 0),
            $pct($svc['time_unknown'] ?? 0)
        ];
    }

    $headers = ['Service', 'OK %', 'Warn %', 'Crit %', 'Unk %'];

    // Service availability table with color highlighting like JavaScript
    $pdf->SetY($contentStartY);

    // Table title row exactly like JavaScript
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->SetFillColor(230, 230, 230);
    $pdf->SetTextColor(51, 51, 51);
    $totalWidth = $pageWidth - 2 * $pageMargin;
    $pdf->Cell($totalWidth, 8, 'Service Availability Summary', 1, 1, 'L', true);

    // Headers
    $pdf->SetFont('Arial', 'B', 8);
    $pdf->SetFillColor(245, 245, 245);
    $serviceColWidth = $totalWidth * 0.4;
    $pctColWidth = $totalWidth * 0.15;

    $pdf->Cell($serviceColWidth, 6, $headers[0], 1, 0, 'C', true);
    for ($i = 1; $i < count($headers); $i++) {
        $pdf->Cell($pctColWidth, 6, $headers[$i], 1, 0, 'C', true);
    }
    $pdf->Ln();

    // Data rows with color highlighting exactly like JavaScript
    $pdf->SetFont('Arial', '', 8);
    foreach ($bodyRowsRaw as $row) {
        // Service name (no highlight)
        $pdf->SetFillColor(255, 255, 255);
        $pdf->Cell($serviceColWidth, 6, $row[0], 1, 0, 'L', true);

        // Percentage columns with color highlighting
        for ($i = 1; $i < count($row); $i++) {
            $num = floatval($row[$i]);
            $fill = false;
            if ($num > 0) {
                switch ($i) {
                    case 1: // OK
                        $pdf->SetFillColor(165, 214, 167);
                        $fill = true;
                        break;
                    case 2: // Warning
                        $pdf->SetFillColor(255, 224, 130);
                        $fill = true;
                        break;
                    case 3: // Critical
                        $pdf->SetFillColor(229, 115, 115);
                        $fill = true;
                        break;
                    case 4: // Unknown
                        $pdf->SetFillColor(169, 182, 201);
                        $fill = true;
                        break;
                }
                if ($fill) {
                    $pdf->SetTextColor(51, 51, 51);
                }
            } else {
                $pdf->SetFillColor(255, 255, 255);
            }

            $pdf->Cell($pctColWidth, 6, $row[$i], 1, 0, 'R', $fill);
            if ($fill) {
                $pdf->SetTextColor(0, 0, 0); // Reset
            }
        }
        $pdf->Ln();
    }
}

// -----------------------------------------------------------------------------
// Service state change timelines (exactly like JavaScript version)
// -----------------------------------------------------------------------------
if (!empty($services)) {
    // Fetch service timelines exactly like JavaScript
    $svcColorMap = [
        'OK' => [165, 214, 167], 'ok' => [165, 214, 167], '2' => [165, 214, 167], '0' => [165, 214, 167],
        'WARNING' => [255, 224, 130], 'warning' => [255, 224, 130], '4' => [255, 224, 130], '1' => [255, 224, 130],
        'CRITICAL' => [229, 115, 115], 'critical' => [229, 115, 115], '16' => [229, 115, 115],
        'UNKNOWN' => [169, 182, 201], 'unknown' => [169, 182, 201], '8' => [169, 182, 201], '3' => [169, 182, 201]
    ];

    foreach ($services as $svc) {
        // Fetch timeline for this service
        $svcTimelineUrl = "/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=service&hostname=" . urlencode($hostname) . "&servicedescription=" . urlencode($svc['description']) . "&starttime={$startTs}&endtime={$endTs}";
        $svcTlJs = fetchJson($svcTimelineUrl);

        // Filter and sort exactly like JavaScript
        $rawList = $svcTlJs['statechangelist'] ?? [];
        $filteredList = array_filter($rawList, function($entry) {
            return !isset($entry['plugin_output']) || !str_contains($entry['plugin_output'], 'Pseudo-State');
        });
        usort($filteredList, function($a, $b) {
            return $a['timestamp'] - $b['timestamp'];
        });

        if (empty($filteredList)) continue;

        // Add new page for each service like JavaScript
        $pdf->AddPage();
        $curY = $contentStartY;

        // Service title
        $pdf->SetFont('Arial', '', 11);
        $pdf->SetXY($pageMargin, $curY);
        $pdf->Cell(0, 0, "Service Trends - {$svc['description']}");
        $curY += 6;

        // Draw timeline bar exactly like JavaScript
        $barHeight = 8;
        $barWidth = $pageWidth - 2 * $pageMargin;
        $startMs = $startTs * 1000;
        $endMs = $endTs * 1000;
        $duration = $endMs - $startMs;

        // Draw timeline background
        $pdf->SetFillColor(245, 245, 245);
        $pdf->Rect($pageMargin, $curY, $barWidth, $barHeight, 'F');
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->SetLineWidth(0.5);
        $pdf->Rect($pageMargin, $curY, $barWidth, $barHeight, 'D');

        // Draw timeline segments
        for ($i = 0; $i < count($filteredList); $i++) {
            $ev = $filteredList[$i];
            $segStart = $i === 0 ? $startMs : $ev['timestamp'];
            $segEnd = ($i < count($filteredList) - 1) ? $filteredList[$i + 1]['timestamp'] : $endMs;
            $s = max($segStart, $startMs);
            $e = min($segEnd, $endMs);
            if ($e <= $s) continue;

            $x = $pageMargin + (($s - $startMs) / $duration) * $barWidth;
            $w = max(1, (($e - $s) / $duration) * $barWidth);
            $col = $svcColorMap[$ev['state']] ?? [200, 200, 200];

            // Draw segment with gradient effect
            $pdf->SetFillColor($col[0], $col[1], $col[2]);
            $pdf->Rect($x, $curY + 1, $w, $barHeight - 2, 'F');

            // Add subtle highlight on top
            $lighterCol = array_map(function($c) { return min(255, $c + 20); }, $col);
            $pdf->SetFillColor($lighterCol[0], $lighterCol[1], $lighterCol[2]);
            $pdf->Rect($x, $curY + 1, $w, 2, 'F');

            // Add segment borders for definition
            if ($w > 2) {
                $pdf->SetDrawColor(0, 0, 0);
                $pdf->SetLineWidth(0.2);
                $pdf->Line($x, $curY + 1, $x, $curY + $barHeight - 1);
                $pdf->Line($x + $w, $curY + 1, $x + $w, $curY + $barHeight - 1);
            }
        }

        $curY += $barHeight + 6;

        // Table of changes
        $pdf->SetY($curY);
        $pdf->SetFont('Arial', 'B', 8);
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(51, 51, 51);

        // Headers
        $timeColWidth = ($pageWidth - 2 * $pageMargin) * 0.7;
        $stateColWidth = ($pageWidth - 2 * $pageMargin) * 0.3;
        $pdf->Cell($timeColWidth, 6, 'Timestamp', 1, 0, 'C', true);
        $pdf->Cell($stateColWidth, 6, 'State', 1, 0, 'C', true);
        $pdf->Ln();

        // Data rows
        $pdf->SetFont('Arial', '', 8);
        $pdf->SetFillColor(255, 255, 255);
        foreach ($filteredList as $ev) {
            $stTxt = isset($ev['state_text']) ? strtoupper($ev['state_text']) : stateLabel($ev['state'], false);
            $pdf->Cell($timeColWidth, 6, date('Y-m-d H:i:s', $ev['timestamp']), 1, 0, 'L');
            $pdf->Cell($stateColWidth, 6, $stTxt, 1, 0, 'C');
            $pdf->Ln();
        }
    }
}

// -----------------------------------------------------------------------------
// Finalize PDF and output (exactly like JavaScript version)
// -----------------------------------------------------------------------------
// Headers and footers are automatically handled by FPDF Header() method

// Generate filename exactly like JavaScript
$ts = date('Y-m-d-H-i-s');
$filename = "availability_report_{$hostname}_{$ts}.pdf";
$filepath = $TMP_DIR . '/' . $filename;

// Output PDF to file
$pdf->Output('F', $filepath);

// Check if file was created successfully
if (!file_exists($filepath)) {
    echo json_encode(['success' => false, 'message' => 'Failed to generate PDF file']);
    exit;
}

// Return success response with download information
echo json_encode([
    'success' => true,
    'message' => 'Report generated successfully',
    'filename' => $filename,
    'filepath' => $filepath,
    'download_url' => 'functions/hostApmFunctions/downloadReport.php?file=' . urlencode($filename)
]);
?>