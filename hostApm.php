<div class="container">
    <div class="status-header">
        <div id="apmTitle" class="hostname">Application Performance Monitor</div>
        <div id="service-count" class="status-badge"><i class="fa fa-server" aria-hidden="true"></i> Loading...</div>
    </div>
    <div class="host-column">
        <div id="host-card" class="host-card"></div>
    </div>
    <div class="services-column">
        <!-- Host availability graph -->
        <div id="availability-container" class="availability-container">
            <div class="availability-header">
                <h3 id="availability-title" style="margin:0; font-size:16px;">Availability (last 24 h)</h3>
                <div class="availability-controls">
                    <input type="datetime-local" id="availability-start" title="Start time">
                    <input type="datetime-local" id="availability-end" title="End time">
                    <button id="availability-refresh" class="availability-refresh" title="Refresh availability graph"><i class="fa fa-refresh" aria-hidden="true"></i></button>
                </div>
            </div>
            <div id="availability-chart" class="availability-chart"></div>
        </div>
        <!-- Add search bar -->
        <div class="search-container">
            <div class="search-wrapper">
                <i class="fa fa-search search-icon" aria-hidden="true"></i>
                <input type="text" id="service-search" class="service-search" placeholder="Search services..." aria-label="Search services">
                <button id="clear-search" class="clear-search" aria-label="Clear search"><i class="fa fa-times" aria-hidden="true"></i></button>
            </div>
            <div class="filter-container">
                <div class="filter-label">Filter by status:</div>
                <div class="filter-buttons">
                    <button class="filter-btn" data-status="all">All</button>
                    <button class="filter-btn ok-filter" data-status="ok">OK</button>
                    <button class="filter-btn warning-filter" data-status="warning">Warning</button>
                    <button class="filter-btn critical-filter" data-status="critical">Critical</button>
                    <button class="filter-btn unknown-filter" data-status="unknown">Unknown</button>
                    <button class="filter-btn pending-filter" data-status="pending">Pending</button>
                </div>
            </div>
        </div>
        <div id="status" class="status-container">
            <div class="loading">Loading service status...</div>
        </div>
    </div>
</div>

<!-- Add AI assistant bubble -->
<div id="ai-assistant-bubble" class="ai-bubble">
    <div class="ai-bubble-content">
        <div class="ai-bubble-header">
            <img src="imgs/icons/ai-avatar.png" alt="AI Assistant" class="ai-avatar">
            <span>AI Assistant</span>
            <button id="close-ai-bubble" class="close-bubble">&times;</button>
        </div>
        <div class="ai-bubble-message">
            I've detected a service with an OID error. This typically indicates a missing SNMP MIB type. Please contact support so we can add the required OID files for proper monitoring.
        </div>
    </div>
</div>

<style>
.ai-bubble {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: #ffebee;
    border-radius: var(--radius, 10px);
    box-shadow: var(--shadow, 0 4px 12px rgba(0,0,0,0.15));
    z-index: 1001;
    display: none;
    animation: bubbleIn 0.3s ease-out;
    overflow: hidden;
    border: 1px solid #ffcdd2;
}

@keyframes bubbleIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.ai-bubble-content {
    padding: 15px;
}

.ai-bubble-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #ffcdd2;
    padding-bottom: 8px;
}

.ai-avatar {
    width: 65px;
    height: 65px;
    margin-right: 10px;
    filter: var(--avatar-filter, none);
}

.ai-bubble-header span {
    color: #d32f2f;
    font-weight: 600;
}

.close-bubble {
    margin-left: auto;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #d32f2f;
}

.close-bubble:hover {
    color: #b71c1c;
}

.ai-bubble-message {
    font-size: 14px;
    line-height: 1.4;
    color: #d32f2f;
}
</style>

<div id="custom-context-menu" style="display:none; position:absolute; z-index:1001;"></div>
<div id="service-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modal-title"></h2>
            <span id="modal-refresh" class="modal-refresh" style="display: none;"><i class="fa fa-refresh" aria-hidden="true"></i></span>
            <span id="modal-delete" class="modal-delete" style="display: none;"><i class="fa fa-trash"
                    aria-hidden="true"></i></span>
            <span id="modal-options" class="modal-options"><i class="fa fa-ellipsis-v" aria-hidden="true"></i></span>
            <span id="modal-close" class="modal-close">×</span>
        </div>
        <div id="modal-body" class="modal-body"></div>
    </div>
</div>
<script src="functions/hostApmFunctions/helperFunctions.js"></script>
<script src="functions/hostApmFunctions/iframeHandler.js"></script>
<script src="functions/hostApmFunctions/modalHandler.js"></script>
<script src="functions/hostApmFunctions/statusDatHandler.js"></script>
<script src="functions/hostApmFunctions/aiAssistant.js"></script>
<script src="functions/hostApmFunctions/openModals.js"></script>
<script src="functions/hostApmFunctions/contextMenu.js"></script>
<script src="functions/hostApmFunctions/troubleshooting.js"></script>
<script src="functions/hostApmFunctions/nagiosComments.js"></script>
<script src="functions/hostApmFunctions/deleteService.js"></script>
<script src="functions/hostApmFunctions/searchServices.js"></script>
<script src="functions/hostApmFunctions/viewToggle.js"></script>
<script src="functions/d3.v7.min.js"></script>
<script src="functions/hostApmFunctions/availabilityGraph.js"></script>
<script src="functions/reportsFunctions/jspdf.umd.min.js"></script>
<script src="functions/reportsFunctions/jspdf.plugin.autotable.min.js"></script>
<script src="functions/hostApmFunctions/pdfAvailabilityReport.js"></script>
<script src="functions/hostApmFunctions/hostApm.js"></script>